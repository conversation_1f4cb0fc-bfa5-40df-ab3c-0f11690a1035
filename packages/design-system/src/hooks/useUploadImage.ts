import { Alert } from '../helpers';
import { useAppLoadingStore, useAppStore, useUserStore } from '../stores';
import type { IImage } from '../types';
import { useI18n } from './useI18n';

/**
 * Hook for handling image uploads across all microservices
 * Purpose: Provides centralized image upload functionality with proper error handling and loading states
 * @returns {object} Object containing image upload functions
 */
export const useUploadImage = () => {
  const { t } = useI18n('common');
  const { user } = useUserStore();
  const { isoCode } = useAppStore();
  const { showLoading, hideLoading } = useAppLoadingStore();

  /**
   * Generates a unique string for image naming
   * Purpose: Creates unique identifiers for uploaded images
   * @returns {string} Unique string identifier
   */
  const genUniqueString = (): string => {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  };

  /**
   * Checks if an image URI is local (not a remote URL)
   * Purpose: Determines if image needs to be uploaded or is already remote
   * @param uri - Image URI to check
   * @returns {boolean} True if image is local, false if remote
   */
  const isImageLocal = (uri?: string): boolean => {
    if (!uri) return false;
    return (
      uri.startsWith('file://') ||
      (!uri.startsWith('http://') && !uri.startsWith('https://'))
    );
  };

  /**
   * Processes a single image for upload
   * Purpose: Handles individual image processing and upload preparation
   * @param image - Image object containing URI and metadata
   * @param endPath - Optional path suffix for upload destination
   * @param keyPrefix - Optional prefix for upload key
   * @returns {Promise<IImage | undefined>} Processed image object with link
   */
  const getLinkImage = async ({
    image,
    endPath,
    keyPrefix,
  }: {
    image: IImage;
    endPath?: string;
    keyPrefix?: string;
  }): Promise<IImage | undefined> => {
    try {
      if (isImageLocal(image.uri)) {
        // For now, return the local image as-is
        // In a full implementation, this would upload to AWS S3
        const uniqueString = genUniqueString();
        const processedImage: IImage = {
          ...image,
          _id: uniqueString,
          oldName: image.name,
          type: image.type || 'image/png',
          name: `${image.name}-${uniqueString}`,
          link: image.uri, // In production, this would be the S3 URL
        };

        // Simulate upload delay
        await new Promise((resolve) => setTimeout(resolve, 500));

        return processedImage;
      } else {
        // Image is already remote, return as-is
        return {
          ...image,
          oldName: image.name,
          link: image.uri,
        };
      }
    } catch (error) {
      Alert.alert.open?.({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('UPLOAD_IMAGES_FAIL'),
        actions: [{ text: t('CLOSE') }],
      });
    }
  };

  /**
   * Processes multiple images for upload
   * Purpose: Handles batch image processing and upload preparation
   * @param images - Array of image objects to process
   * @param endPath - Optional path suffix for upload destination
   * @param keyPrefix - Optional prefix for upload key
   * @returns {Promise<IImage[]>} Array of processed image objects with links
   */
  const getMultipleLinkImages = async ({
    images,
    endPath,
    keyPrefix,
  }: {
    images: IImage[];
    endPath?: string;
    keyPrefix?: string;
  }): Promise<IImage[]> => {
    try {
      showLoading();

      const imageResponses = await Promise.all(
        images.map(async (image) => {
          return await getLinkImage({ image, endPath, keyPrefix });
        }),
      );

      const validImages = imageResponses.filter(
        (image): image is IImage => !!image,
      );

      hideLoading();
      return validImages;
    } catch (error) {
      hideLoading();
      Alert.alert.open?.({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('UPLOAD_IMAGES_FAIL'),
        actions: [{ text: t('CLOSE') }],
      });
      return [];
    }
  };

  /**
   * Shows error toast for maximum images reached
   * Purpose: Displays user-friendly error message when image limit is exceeded
   * @returns {void} No return value, shows toast notification
   */
  const showToastMaxImages = (): void => {
    Alert.alert.open?.({
      title: t('DIALOG_TITLE_INFORMATION'),
      message: t('MAXIMUM_IMAGES_REACHED'),
      actions: [{ text: t('CLOSE') }],
    });
  };

  return {
    getLinkImage,
    getMultipleLinkImages,
    showToastMaxImages,
    isImageLocal,
  };
};
